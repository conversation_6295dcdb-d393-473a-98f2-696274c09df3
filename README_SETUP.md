# 🚀 QUOTEX TRADING BOT SYSTEM - Setup Guide

## ✨ Features Implemented

### 🎯 Menu System
- **Practice Mode**: Signal display only (no real trading)
- **Demo Trading**: Trade on Quotex demo account
- **Live Trading**: Trade with real money (requires confirmation)
- **Balance Check**: Check both demo and live account balances
- **Exit**: Graceful shutdown

### 📊 Trading Pairs
- **Live Pairs**: First 20 pairs (1-20) fetched from OANDA API
- **OTC Pairs**: 30 pairs (21-50) fetched from Quotex API
- **Proper Numbering**: OTC pairs start from 21 as requested

### ⏰ Timeframes
- **Enhanced Support**: 1m, 2m, 3m, 5m, 15m, 30m, 1h, 4h, 1d
- **Accurate Timing**: Proper calculation for each timeframe

### 🎨 Beautiful Signal Display
- **Exact Format**: As requested with emojis and colors
- **Signal Time**: Shows next candle time (current time + 2 seconds)
- **Processing Time**: Shows actual processing duration
- **No Unnecessary Logs**: Clean output without timestamp clutter

### 🛡️ Error Handling
- **Graceful Shutdown**: Beautiful message when Ctrl+C is pressed
- **No Traceback**: Clean error handling throughout

## 🛠️ Installation & Setup

### 1. Install Dependencies
```bash
pip install -r requirements_quotex.txt
```

### 2. Update QuotexPy URLs (IMPORTANT!)
```bash
python quotexpy_patch.py
```
This updates the quotexpy library to use the new working URLs:
- Main URL: https://qxbroker.net/
- Login URL: https://market-qx.pro/en/sign-in/

### 3. Environment Setup
The `.env` file is already configured with your credentials:
```
QUOTEX_EMAIL=<EMAIL>
QUOTEX_PASSWORD=Uz2309##2309
```

## 🚀 Running the Bot

### Method 1: Direct Run
```bash
python quotex_trading_bot.py
```

### Method 2: Using Launcher
```bash
python run_bot.py
```

## 📋 Menu Options

### 1. 📊 Practice Mode
- **Purpose**: Signal display only, no real trading
- **Features**: 
  - Select trading pairs (Live + OTC)
  - Choose timeframes (including 2m, 3m)
  - Pick trading strategies
  - Beautiful signal display
  - No Quotex connection required

### 2. 🎯 Demo Trading
- **Purpose**: Trade on Quotex demo account
- **Features**:
  - Connects to Quotex automatically
  - Uses demo account balance
  - Places actual demo trades
  - Real-time balance updates

### 3. 💰 Live Trading
- **Purpose**: Trade with real money
- **Features**:
  - Requires 'CONFIRM' to proceed
  - Connects to live Quotex account
  - Places real trades with real money
  - Balance verification before trading

### 4. 💳 Balance Check
- **Purpose**: Check account balances
- **Features**:
  - Shows both demo and live balances
  - No trading, just balance display
  - Quick connection test

### 5. ❌ Exit
- **Purpose**: Graceful shutdown
- **Features**:
  - Clean exit message
  - No error traces

## 🎯 Signal Display Format

The bot displays signals in the exact format requested:

```
================================================================================
                      📊 MARKET SCAN - 2025-07-11 02:29:30
================================================================================
========================================================================================================================
💱 PAIR            | 📅 DATE            | 🕐 TIME          | 📈📉 DIRECTION   | 🎯 CONFIDENCE  | 💰 PRICE         | 🔧 STRATEGY
========================================================================================================================
💱 EURUSD          | 📅 2025-07-11      | 🕐 02:29:32      | 🟢 Call          | 🎯 85%         | 💰  1.08567      | 🔧 Support Resistance
========================================================================================================================
✅ Found 1 trading signal
Trade executed: CALL EURUSD
⏳ Processing took 0.00s.
⏳ Waiting 58 seconds until next scan...
```

## 🔧 Technical Details

### URL Updates Applied
- ✅ qxbroker.com → qxbroker.net
- ✅ Login URL → market-qx.pro/en/sign-in
- ✅ WebSocket URLs updated
- ✅ All quotexpy files patched

### Timeframe Support
- ✅ 2m and 3m timeframes added
- ✅ Proper interval calculations
- ✅ Accurate next scan timing

### Error Handling
- ✅ Graceful Ctrl+C shutdown
- ✅ Beautiful error messages
- ✅ No Python tracebacks shown

### Performance Optimizations
- ✅ Faster processing
- ✅ Reduced unnecessary logging
- ✅ Optimized signal generation

## 🧪 Testing

### Test Signal Display
```bash
python test_signal_display.py
```
This shows various signal scenarios without waiting for real timing.

## 📁 File Structure

```
Train Bot/
├── quotex_trading_bot.py      # Main trading bot
├── quotexpy_patch.py          # URL patcher for quotexpy
├── test_signal_display.py     # Signal display tester
├── run_bot.py                 # Simple launcher
├── requirements_quotex.txt    # Dependencies
├── .env                       # Credentials (configured)
├── README_SETUP.md           # This setup guide
└── README_QUOTEX.md          # Original documentation
```

## 🎉 Ready to Use!

The bot is fully configured and ready to use with all requested features:

1. ✅ Beautiful menu system with emojis and colors
2. ✅ Proper pair numbering (Live 1-20, OTC 21-50)
3. ✅ Enhanced timeframes (2m, 3m support)
4. ✅ Exact signal display format
5. ✅ Graceful error handling
6. ✅ Updated Quotex URLs
7. ✅ Clean output (no unnecessary logs)
8. ✅ Proper signal timing (next candle time)
9. ✅ Fast processing
10. ✅ All modes working (Practice, Demo, Live, Balance)

**Enjoy your enhanced Quotex Trading Bot System! 🚀**
