#!/usr/bin/env python3
"""
QuotexPy URL Patch
Updates the URLs in quotexpy library to use the new working URLs
"""

import os
import sys
import importlib.util

def patch_quotexpy_urls():
    """Patch quotexpy URLs to use new working URLs"""
    try:
        # Try to import quotexpy to check if it's installed
        import quotexpy
        
        # Get the quotexpy installation path
        quotexpy_path = os.path.dirname(quotexpy.__file__)
        
        print(f"Found quotexpy at: {quotexpy_path}")
        
        # Files to patch and their URL replacements
        files_to_patch = {
            'http/qxbroker.py': {
                'base_url = "qxbroker.com"': 'base_url = "qxbroker.net"',
                'f"{self.https_base_url}/en/sign-in"': 'f"https://market-qx.pro/en/sign-in"'
            },
            'http/login.py': {
                'base_url = "qxbroker.com"': 'base_url = "qxbroker.net"',
                'https_base_url = f"https://{base_url}"': 'https_base_url = f"https://{base_url}"'
            },
            'http/logout.py': {
                'base_url = "qxbroker.com"': 'base_url = "qxbroker.net"'
            },
            'api.py': {
                '"origin": "https://qxbroker.com"': '"origin": "https://qxbroker.net"',
                '"host": "ws2.qxbroker.com"': '"host": "ws2.qxbroker.net"'
            }
        }
        
        patched_files = []
        
        for file_path, replacements in files_to_patch.items():
            full_path = os.path.join(quotexpy_path, file_path)
            
            if os.path.exists(full_path):
                try:
                    # Read the file
                    with open(full_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # Apply replacements
                    modified = False
                    for old_text, new_text in replacements.items():
                        if old_text in content:
                            content = content.replace(old_text, new_text)
                            modified = True
                    
                    # Write back if modified
                    if modified:
                        # Create backup
                        backup_path = full_path + '.backup'
                        if not os.path.exists(backup_path):
                            with open(backup_path, 'w', encoding='utf-8') as f:
                                f.write(content.replace(new_text, old_text))
                        
                        # Write patched version
                        with open(full_path, 'w', encoding='utf-8') as f:
                            f.write(content)
                        
                        patched_files.append(file_path)
                        print(f"✅ Patched: {file_path}")
                    else:
                        print(f"ℹ️  No changes needed: {file_path}")
                        
                except Exception as e:
                    print(f"❌ Error patching {file_path}: {str(e)}")
            else:
                print(f"⚠️  File not found: {file_path}")
        
        if patched_files:
            print(f"\n✅ Successfully patched {len(patched_files)} files:")
            for file_path in patched_files:
                print(f"   - {file_path}")
            print("\n🔄 Please restart your Python application to use the updated URLs.")
        else:
            print("\n✅ All files are already up to date or no files needed patching.")
            
        return True
        
    except ImportError:
        print("❌ quotexpy is not installed. Please install it first:")
        print("   pip install quotexpy")
        return False
    except Exception as e:
        print(f"❌ Error patching quotexpy: {str(e)}")
        return False

def restore_quotexpy_urls():
    """Restore original quotexpy URLs from backup"""
    try:
        import quotexpy
        quotexpy_path = os.path.dirname(quotexpy.__file__)
        
        restored_files = []
        
        # Find all backup files
        for root, dirs, files in os.walk(quotexpy_path):
            for file in files:
                if file.endswith('.backup'):
                    backup_path = os.path.join(root, file)
                    original_path = backup_path[:-7]  # Remove .backup extension
                    
                    try:
                        # Read backup content
                        with open(backup_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                        
                        # Restore original file
                        with open(original_path, 'w', encoding='utf-8') as f:
                            f.write(content)
                        
                        # Remove backup
                        os.remove(backup_path)
                        
                        restored_files.append(os.path.relpath(original_path, quotexpy_path))
                        
                    except Exception as e:
                        print(f"❌ Error restoring {original_path}: {str(e)}")
        
        if restored_files:
            print(f"✅ Restored {len(restored_files)} files to original state:")
            for file_path in restored_files:
                print(f"   - {file_path}")
        else:
            print("ℹ️  No backup files found to restore.")
            
        return True
        
    except Exception as e:
        print(f"❌ Error restoring quotexpy: {str(e)}")
        return False

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "restore":
        print("🔄 Restoring original quotexpy URLs...")
        restore_quotexpy_urls()
    else:
        print("🔧 Patching quotexpy URLs...")
        patch_quotexpy_urls()
