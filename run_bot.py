#!/usr/bin/env python3
"""
Simple launcher for the Quotex Trading Bot
"""

import subprocess
import sys
import os

def main():
    """Launch the trading bot"""
    try:
        # Change to the script directory
        script_dir = os.path.dirname(os.path.abspath(__file__))
        os.chdir(script_dir)
        
        # Run the trading bot
        subprocess.run([sys.executable, "quotex_trading_bot.py"], check=True)
        
    except KeyboardInterrupt:
        print("\n👋 Bot stopped by user")
    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    main()
