#!/usr/bin/env python3
"""
Test script to demonstrate the signal display functionality
"""

import asyncio
import time
from datetime import datetime
from quotex_trading_bot import display_signal, get_signal, print_colored

async def test_signal_display():
    """Test the signal display with different scenarios"""
    
    print_colored("🧪 Testing Signal Display Functionality", "cyan", bold=True)
    print_colored("="*80, "blue")
    
    # Test 1: Signal with Call direction
    print_colored("\n📊 Test 1: Call Signal", "yellow", bold=True)
    start_time = time.time()
    signal, confidence = "call", 0.85
    price = 1.08567
    processing_time = time.time() - start_time
    display_signal("EURUSD", signal, confidence, price, "support_resistance", processing_time)
    
    await asyncio.sleep(2)
    
    # Test 2: Signal with Put direction
    print_colored("\n📊 Test 2: Put Signal", "yellow", bold=True)
    start_time = time.time()
    signal, confidence = "put", 0.72
    price = 1.08432
    processing_time = time.time() - start_time
    display_signal("GBPUSD", signal, confidence, price, "momentum_breakout", processing_time)
    
    await asyncio.sleep(2)
    
    # Test 3: No Signal
    print_colored("\n📊 Test 3: No Signal", "yellow", bold=True)
    start_time = time.time()
    signal, confidence = "no signal", 0.0
    price = 1.23456
    processing_time = time.time() - start_time
    display_signal("USDJPY_OTC", signal, confidence, price, "trend_following", processing_time)
    
    await asyncio.sleep(2)
    
    # Test 4: Random signal generation
    print_colored("\n📊 Test 4: Random Signal Generation", "yellow", bold=True)
    pairs = ["EURUSD", "GBPUSD_OTC", "XAUUSD_OTC", "BTCUSD_OTC"]
    strategies = ["support_resistance", "momentum_breakout", "trend_following", "rsi_oversold"]
    
    for i, (pair, strategy) in enumerate(zip(pairs, strategies)):
        start_time = time.time()
        signal, confidence = get_signal(pair, "1m", strategy)
        price = 1.0 + (i * 0.1) + 0.05432
        processing_time = time.time() - start_time
        
        print_colored(f"\n🔍 Test 4.{i+1}: {pair} with {strategy}", "green")
        display_signal(pair, signal, confidence, price, strategy, processing_time)
        
        if i < len(pairs) - 1:
            await asyncio.sleep(1)
    
    print_colored("\n✅ Signal display tests completed!", "green", bold=True)

if __name__ == "__main__":
    asyncio.run(test_signal_display())
