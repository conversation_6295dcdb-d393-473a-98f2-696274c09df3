# Quotex Trading Bot System Requirements
# Install with: pip install -r requirements_quotex.txt

# Core dependencies
quotexpy>=1.40.0
python-dotenv>=0.19.0

# Data analysis and processing
pandas>=1.3.0
numpy>=1.21.0

# HTTP requests and API handling
requests>=2.25.0
aiohttp>=3.8.0

# Web scraping and browser automation
beautifulsoup4>=4.9.0
undetected-chromedriver>=3.4.0

# WebSocket support
websocket-client>=1.3.0

# Async support (built-in with Python 3.7+)
# asyncio>=3.4.3

# OANDA API support (for live pairs)
oandapyV20>=0.6.3

# Optional: For advanced technical analysis
# ta-lib>=0.4.0  # Technical Analysis Library (optional - requires manual installation)
# plotly>=5.0.0  # For plotting (optional)

# Development and testing (optional)
# pytest>=6.0.0
# black>=21.0.0
# flake8>=3.9.0
